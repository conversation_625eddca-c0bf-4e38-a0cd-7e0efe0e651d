"""认证相关API接口"""

import logging
from fastapi import <PERSON><PERSON><PERSON><PERSON>, Depends, HTTPException, status, Request
from fastapi.security import HTT<PERSON><PERSON>earer
from sqlalchemy.orm import Session
from sqlalchemy.exc import SQLAlchemyError

from schemas.auth import LoginRequest, LoginResponse, UserResponse
from services.auth_service import AuthService
from services.cache_service import CacheService
from crud.database import get_database_session
from utils.dependencies import get_current_username
from utils.exceptions import AuthenticationError, DatabaseError

logger = logging.getLogger(__name__)
router = APIRouter(prefix="/api", tags=["auth"])
security = HTTPBearer()


@router.post("/login", response_model=LoginResponse)
async def login(
    login_data: LoginRequest,
    db: Session = Depends(get_database_session)
):
    """
    用户登录接口
    """
    try:
        # 验证用户凭据
        token_data = AuthService.authenticate_and_generate_token(
            db=db,
            username=login_data.username,
            password=login_data.password
        )

        # 将令牌存入缓存
        await CacheService.set_token(token_data.access_token, login_data.username)

        logger.info(f"User {login_data.username} logged in successfully")
        return token_data

    except AuthenticationError as e:
        logger.warning(f"Authentication failed for user {login_data.username}: {e}")
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="用户名或密码错误",
            headers={"WWW-Authenticate": "Bearer"},
        )
    except DatabaseError as e:
        logger.error(f"Database error during login for user {login_data.username}: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="登录时发生数据库错误"
        )
    except SQLAlchemyError as e:
        logger.error(f"SQLAlchemy error during login for user {login_data.username}: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="登录时发生数据库错误"
        )
    except Exception as e:
        logger.error(f"Unexpected error during login for user {login_data.username}: {e}", exc_info=True)
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="登录时发生未知错误"
        )


@router.get("/user/me", response_model=UserResponse)
def get_current_user_info(
    username: str = Depends(get_current_username)
):
    """
    获取当前用户信息
    """
    try:
        logger.debug(f"Retrieving info for user {username}")
        return UserResponse(username=username)
    except Exception as e:
        logger.error(f"Error retrieving info for user {username}: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="获取用户信息失败"
        )


@router.post("/logout")
async def logout(
    request: Request,
    username: str = Depends(get_current_username)
):
    """
    用户登出接口，清除认证状态并使访问令牌失效
    """
    try:
        # 从请求头中获取令牌
        auth_header = request.headers.get("Authorization")
        if auth_header and auth_header.startswith("Bearer "):
            token = auth_header.split(" ")[1]
            # 从缓存中移除令牌
            await CacheService.remove_token(token)
            logger.info(f"User {username} logged out successfully")
            return {"message": "登出成功"}
        else:
            logger.warning(f"No valid token found in logout request for user {username}")
            return {"message": "登出成功"}
    except Exception as e:
        logger.error(f"Error during logout for user {username}: {e}", exc_info=True)
        # 即使出现错误也返回成功，确保用户体验
        return {"message": "登出成功"}