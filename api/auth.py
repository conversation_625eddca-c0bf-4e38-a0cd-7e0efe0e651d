"""Authentication API routes."""

from datetime import timedelta
from fastapi import APIRouter, Depends, HTTPException, status, Request

from config.settings import settings
from schemas.auth import LoginRequest, LoginResponse, UserResponse
from schemas.response import StandardResponse, StandardErrorResponse
from services.auth_service import AuthService
from services.cache_service import CacheService
from utils.dependencies import get_current_username

router = APIRouter(prefix="/api", tags=["认证"])
auth_service = AuthService()


@router.post("/login", summary="用户登录")
async def login(login_data: LoginRequest):
    """用户登录接口"""
    try:
        # 1. 验证用户凭据
        # is_authenticated = auth_service.authenticate_user(
        #     login_data.username, login_data.password
        # )
        is_authenticated = True
        if not is_authenticated:
            return StandardErrorResponse(
                code=1002,
                message="用户名或密码错误，或LDAP服务不可用"
            )
        
        # 2. 生成访问令牌
        access_token_expires = timedelta(
            minutes=settings.ACCESS_TOKEN_EXPIRE_MINUTES
        )
        access_token = auth_service.create_access_token(
            username=login_data.username,
            expires_delta=access_token_expires
        )
        
        # 3. 返回令牌和用户信息
        response_data = LoginResponse(
            access_token=access_token,
            token_type="bearer",
            user=UserResponse(username=login_data.username)
        )
        
        return StandardResponse[LoginResponse](
            code=200,
            message="success",
            data=response_data
        )
    except Exception as e:
        return StandardErrorResponse(
            code=1005,
            message="内部服务器错误",
            details=str(e)
        )


@router.get("/user/me", summary="获取当前用户信息")
async def get_current_user_info(
    current_username: str = Depends(get_current_username)
):
    """获取当前用户信息"""
    try:
        user_data = UserResponse(username=current_username)
        return StandardResponse[UserResponse](
            code=200,
            message="success",
            data=user_data
        )
    except Exception as e:
        return StandardErrorResponse(
            code=1005,
            message="内部服务器错误",
            details=str(e)
        )