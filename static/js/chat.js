const { createApp, ref, onMounted, nextTick } = Vue;

// 消息组件
const MessageItem = {
    name: 'MessageItem',
    props: {
        message: {
            type: Object,
            required: true
        },
        level: {
            type: Number,
            default: 0
        }
    },
    template: `
        <div :style="{ marginLeft: level * 20 + 'px' }" class="message-item">
            <!-- 连接线 -->
            <div v-if="level > 0" class="flex items-start mb-2">
                <div class="w-4 h-4 border-l-2 border-b-2 border-gray-300 rounded-bl-lg mr-2 mt-2"></div>
                <div class="flex-1">
                    <div :class="[
                        'max-w-3/4 px-4 py-3 rounded-2xl shadow-sm',
                        message.role === 'user'
                            ? 'ml-auto bg-gradient-to-r from-blue-500 to-indigo-600 text-white rounded-br-none'
                            : 'mr-auto bg-gray-100 text-gray-800 rounded-bl-none'
                    ]" style="width: fit-content;">
                        <div class="whitespace-pre-wrap">{{ message.content }}</div>
                        <div class="text-xs mt-1 opacity-70">
                            {{ formatTime(message.timestamp || message.created_at) }}
                        </div>
                    </div>
                </div>
            </div>

            <!-- 根消息 -->
            <div v-else class="mb-2">
                <div :class="[
                    'max-w-3/4 px-4 py-3 rounded-2xl shadow-sm',
                    message.role === 'user'
                        ? 'ml-auto bg-gradient-to-r from-blue-500 to-indigo-600 text-white rounded-br-none'
                        : 'mr-auto bg-gray-100 text-gray-800 rounded-bl-none'
                ]" style="width: fit-content;">
                    <div class="whitespace-pre-wrap">{{ message.content }}</div>
                    <div class="text-xs mt-1 opacity-70">
                        {{ formatTime(message.timestamp || message.created_at) }}
                    </div>
                </div>
            </div>

            <!-- 子消息 -->
            <div v-if="message.children && message.children.length > 0" class="ml-4">
                <message-item
                    v-for="child in message.children"
                    :key="child.id"
                    :message="child"
                    :level="level + 1">
                </message-item>
            </div>
        </div>
    `,
    methods: {
        formatTime(timestamp) {
            if (!timestamp) return '';
            const date = new Date(timestamp);
            return date.toLocaleTimeString('zh-CN', { hour: '2-digit', minute: '2-digit' });
        }
    }
};

createApp({
    components: {
        MessageItem
    },
    setup() {
        // 用户信息
        const currentUser = ref('');
        const token = ref('');
        
        // 侧边栏状态
        const isSidebarCollapsed = ref(false);
        
        // 对话状态
        const conversations = ref([]);
        const currentConversationId = ref(null);
        const messages = ref([]);
        
        // 菜单状态
        const openConversationMenuId = ref(null);
        
        // 消息状态
        const newMessage = ref('');
        const isSending = ref(false);
        const isReceiving = ref(false);
        
        // DOM引用
        const messagesContainer = ref(null);
        const messageInput = ref(null);
        
        // 格式化时间
        const formatTime = (timestamp) => {
            const date = new Date(timestamp);
            return date.toLocaleTimeString('zh-CN', { hour: '2-digit', minute: '2-digit' });
        };
        
        // 切换侧边栏
        const toggleSidebar = () => {
            isSidebarCollapsed.value = !isSidebarCollapsed.value;
        };
        
        // 切换对话菜单
        const toggleConversationMenu = (conversationId) => {
            if (openConversationMenuId.value === conversationId) {
                openConversationMenuId.value = null;
            } else {
                openConversationMenuId.value = conversationId;
            }
        };
        
        // 点击其他地方关闭菜单
        const handleClickOutside = (event) => {
            if (openConversationMenuId.value) {
                // 检查点击的元素是否在菜单内部
                const menuElements = document.querySelectorAll('.conversation-menu');
                let isClickInsideMenu = false;
                
                menuElements.forEach(element => {
                    if (element.contains(event.target)) {
                        isClickInsideMenu = true;
                    }
                });
                
                // 如果点击不在菜单内部，则关闭菜单
                if (!isClickInsideMenu) {
                    openConversationMenuId.value = null;
                }
            }
        };
        
        // 置顶对话
        const pinConversation = async (conversationId) => {
            openConversationMenuId.value = null;

            try {
                // 找到要置顶的对话
                const conversation = conversations.value.find(c => c.id === conversationId);
                if (!conversation) return;

                // 切换置顶状态
                const newStickyFlag = !conversation.sticky_flag;

                // 调用后端API更新置顶状态
                const response = await fetch(`/api/conversations/${conversationId}/sticky`, {
                    method: 'PUT',
                    headers: {
                        'Content-Type': 'application/json',
                        'Authorization': `Bearer ${token.value}`
                    },
                    body: JSON.stringify({
                        sticky_flag: newStickyFlag
                    })
                });

                if (response.ok) {
                    // 更新本地状态
                    conversation.sticky_flag = newStickyFlag;
                    conversation.updated_at = new Date().toISOString();

                    // 重新排序对话列表
                    sortConversations();

                    console.log(`对话 ${conversationId} ${newStickyFlag ? '已置顶' : '已取消置顶'}`);
                } else {
                    console.error('更新置顶状态失败');
                }
            } catch (error) {
                console.error('Pin conversation error:', error);
            }
        };
        
        // 处理登出
        const handleLogout = async () => {
            try {
                // 调用后端登出接口
                await fetch('/api/logout', {
                    method: 'POST',
                    headers: {
                        'Authorization': `Bearer ${token.value}`
                    }
                });
            } catch (error) {
                console.error('Logout error:', error);
            } finally {
                // 无论后端调用是否成功，都清除本地存储
                localStorage.removeItem('token');
                localStorage.removeItem('currentUser');
                window.location.href = '/static/login.html';
            }
        };
        
        // 对话排序函数
        const sortConversations = () => {
            conversations.value.sort((a, b) => {
                // 置顶对话优先
                if (a.sticky_flag && !b.sticky_flag) return -1;
                if (!a.sticky_flag && b.sticky_flag) return 1;

                // 相同置顶状态下按更新时间排序
                return new Date(b.updated_at) - new Date(a.updated_at);
            });
        };

        // 时间分组函数
        const groupConversationsByTime = (conversations) => {
            const now = new Date();
            const today = new Date(now.getFullYear(), now.getMonth(), now.getDate());
            const weekAgo = new Date(today.getTime() - 7 * 24 * 60 * 60 * 1000);
            const monthAgo = new Date(today.getTime() - 30 * 24 * 60 * 60 * 1000);

            const groups = {
                pinned: [],
                today: [],
                week: [],
                month: [],
                older: []
            };

            conversations.forEach(conv => {
                const updatedAt = new Date(conv.updated_at);

                if (conv.sticky_flag) {
                    groups.pinned.push(conv);
                } else if (updatedAt >= today) {
                    groups.today.push(conv);
                } else if (updatedAt >= weekAgo) {
                    groups.week.push(conv);
                } else if (updatedAt >= monthAgo) {
                    groups.month.push(conv);
                } else {
                    groups.older.push(conv);
                }
            });

            return groups;
        };

        // 加载对话列表
        const loadConversations = async () => {
            try {
                const response = await fetch('/api/conversations', {
                    headers: {
                        'Authorization': `Bearer ${token.value}`
                    }
                });

                if (response.ok) {
                    const data = await response.json();
                    conversations.value = data.data.map(conv => ({
                        id: conv.id,
                        title: conv.title || `对话 ${conv.id}`,
                        preview: '暂无消息',
                        created_at: conv.created_at,
                        updated_at: conv.updated_at,
                        sticky_flag: conv.sticky_flag || false
                    }));

                    // 排序对话列表
                    sortConversations();

                    // 如果有对话，默认选择第一个
                    if (conversations.value.length > 0 && !currentConversationId.value) {
                        currentConversationId.value = conversations.value[0].id;
                        await loadMessages(conversations.value[0].id);
                    }
                }
            } catch (error) {
                console.error('Load conversations error:', error);
            }
        };
        
        // 创建新对话
        const createNewConversation = async () => {
            try {
                const response = await fetch('/api/conversations/new', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'Authorization': `Bearer ${token.value}`
                    },
                    body: JSON.stringify({
                        title: `对话 ${new Date().toLocaleDateString('zh-CN')} ${new Date().toLocaleTimeString('zh-CN')}`
                    })
                });

                if (response.ok) {
                    const result = await response.json();
                    const newConversation = result.data;
                    conversations.value.unshift({
                        id: newConversation.id,
                        title: newConversation.title || `对话 ${newConversation.id}`,
                        preview: '暂无消息',
                        created_at: newConversation.created_at,
                        updated_at: newConversation.updated_at,
                        sticky_flag: newConversation.sticky_flag || false
                    });

                    // 重新排序
                    sortConversations();
                    selectConversation(newConversation.id);
                }
            } catch (error) {
                console.error('Create conversation error:', error);
            }
        };
        
        // 选择对话
        const selectConversation = async (conversationId) => {
            if (currentConversationId.value === conversationId) return;
            
            currentConversationId.value = conversationId;
            messages.value = [];
            await loadMessages(conversationId);
        };
        
        // 删除对话
        const deleteConversation = async (conversationId) => {
            if (conversations.value.length <= 1) {
                alert('至少保留一个对话');
                openConversationMenuId.value = null;
                return;
            }
            
            if (!confirm('确定要删除这个对话吗？')) {
                openConversationMenuId.value = null;
                return;
            }
            
            try {
                const response = await fetch(`/api/conversations/${conversationId}`, {
                    method: 'DELETE',
                    headers: {
                        'Authorization': `Bearer ${token.value}`
                    }
                });
                
                if (response.ok) {
                    // 从列表中移除
                    const index = conversations.value.findIndex(c => c.id === conversationId);
                    if (index !== -1) {
                        conversations.value.splice(index, 1);
                    }
                    
                    // 如果删除的是当前对话，切换到第一个对话
                    if (currentConversationId.value === conversationId) {
                        if (conversations.value.length > 0) {
                            selectConversation(conversations.value[0].id);
                        } else {
                            currentConversationId.value = null;
                            messages.value = [];
                        }
                    }
                }
            } catch (error) {
                console.error('Delete conversation error:', error);
            } finally {
                openConversationMenuId.value = null;
            }
        };
        
        // 构建消息树结构
        const buildMessageTree = (messages) => {
            const messageMap = new Map();
            const rootMessages = [];

            // 创建消息映射
            messages.forEach(msg => {
                messageMap.set(msg.id, { ...msg, children: [] });
            });

            // 构建树结构
            messages.forEach(msg => {
                const messageNode = messageMap.get(msg.id);
                if (msg.parent_msg_id === 0) {
                    // 根消息
                    rootMessages.push(messageNode);
                } else {
                    // 子消息
                    const parent = messageMap.get(msg.parent_msg_id);
                    if (parent) {
                        parent.children.push(messageNode);
                    } else {
                        // 如果找不到父消息，作为根消息处理
                        rootMessages.push(messageNode);
                    }
                }
            });

            return rootMessages;
        };

        // 加载消息
        const loadMessages = async (conversationId) => {
            try {
                const response = await fetch(`/api/messages?conversation_id=${conversationId}`, {
                    headers: {
                        'Authorization': `Bearer ${token.value}`
                    }
                });

                if (response.ok) {
                    const result = await response.json();
                    const rawMessages = result.data[conversationId] || [];

                    // 构建消息树结构
                    messages.value = buildMessageTree(rawMessages);
                    scrollToBottom();
                }
            } catch (error) {
                console.error('Load messages error:', error);
            }
        };
        
        // 发送消息
        const sendMessage = async () => {
            const message = newMessage.value.trim();
            if (!message || isSending.value) return;

            newMessage.value = '';

            // 添加用户消息到界面（作为根消息）
            const userMessage = {
                id: Date.now(), // 临时ID
                role: 'user',
                content: message,
                timestamp: new Date().toISOString(),
                parent_msg_id: 0,
                children: []
            };
            messages.value.push(userMessage);

            scrollToBottom();

            isSending.value = true;
            isReceiving.value = true;

            try {
                // 添加AI消息占位符（作为用户消息的回复）
                const aiMessage = {
                    id: Date.now() + 1, // 临时ID
                    role: 'assistant',
                    content: '',
                    timestamp: new Date().toISOString(),
                    parent_msg_id: userMessage.id,
                    children: []
                };

                // 将AI消息添加为用户消息的子消息
                userMessage.children.push(aiMessage);
                
                // 发起流式请求
                const response = await fetch('/api/chat/stream', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'Authorization': `Bearer ${token.value}`
                    },
                    body: JSON.stringify({
                        conversation_id: currentConversationId.value,
                        message: message,
                        collection_name: 'FinancialResearchOffice',
                        input: {}
                    })
                });
                
                if (!response.ok) {
                    throw new Error(`HTTP error! status: ${response.status}`);
                }
                
                const reader = response.body.getReader();
                const decoder = new TextDecoder('utf-8');
                let assistantResponse = '';
                
                while (true) {
                    const { done, value } = await reader.read();
                    
                    if (done) {
                        break;
                    }
                    
                    const chunk = decoder.decode(value, { stream: true });
                    const lines = chunk.split('\n\n');
                    
                    for (const line of lines) {
                        if (line.startsWith('data: ')) {
                            const data = line.substring(6);
                            
                            if (data === '[DONE]') {
                                // 流结束
                                isReceiving.value = false;
                                break;
                            }
                            
                            try {
                                const parsedData = JSON.parse(data);
                                // 检查业务code
                                if (parsedData.code === 200 && parsedData.data?.content) {
                                    // 更新AI回复内容
                                    assistantResponse += parsedData.data.content;
                                    aiMessage.content = assistantResponse;
                                    scrollToBottom();
                                } else if (parsedData.code !== 200 && parsedData.data?.error) {
                                    // 处理错误
                                    aiMessage.content = `错误: ${parsedData.data.error}`;
                                    isReceiving.value = false;
                                    break;
                                }
                            } catch (e) {
                                console.error('解析数据失败:', e);
                            }
                        }
                    }
                    
                    if (!isReceiving.value) {
                        break;
                    }
                }
            } catch (error) {
                console.error('Send message error:', error);
                aiMessage.content = `错误: ${error.message}`;
                isReceiving.value = false;
            } finally {
                isSending.value = false;
                scrollToBottom();
            }
        };
        
        // 处理键盘事件
        const handleKeyDown = (e) => {
            if (e.key === 'Enter' && !e.shiftKey) {
                e.preventDefault();
                sendMessage();
            }
        };
        
        // 滚动到底部
        const scrollToBottom = () => {
            nextTick(() => {
                if (messagesContainer.value) {
                    messagesContainer.value.scrollTop = messagesContainer.value.scrollHeight;
                }
            });
        };
        
        // 检查认证状态
        const checkAuth = () => {
            const storedToken = localStorage.getItem('token');
            const storedUser = localStorage.getItem('currentUser');
            
            if (!storedToken || !storedUser) {
                window.location.href = '/static/login.html';
                return false;
            }
            
            token.value = storedToken;
            currentUser.value = storedUser;
            return true;
        };
        
        // 页面加载时检查认证状态并加载数据
        onMounted(() => {
            if (checkAuth()) {
                loadConversations();
                document.addEventListener('click', handleClickOutside);
            }
        });
        
        return {
            // 状态
            currentUser,
            isSidebarCollapsed,
            conversations,
            currentConversationId,
            messages,
            newMessage,
            isSending,
            isReceiving,
            openConversationMenuId,
            
            // DOM引用
            messagesContainer,
            messageInput,
            
            // 方法
            formatTime,
            toggleSidebar,
            toggleConversationMenu,
            pinConversation,
            handleLogout,
            createNewConversation,
            selectConversation,
            deleteConversation,
            sendMessage,
            handleKeyDown,
            sortConversations,
            groupConversationsByTime,
            buildMessageTree
        };
    }
}).mount('#app');