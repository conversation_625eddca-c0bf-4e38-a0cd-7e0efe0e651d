const { createApp, ref, onMounted, nextTick } = Vue;

createApp({
    setup() {
        // 用户信息
        const currentUser = ref('');
        const token = ref('');
        
        // 侧边栏状态
        const isSidebarCollapsed = ref(false);
        
        // 对话状态
        const conversations = ref([]);
        const currentConversationId = ref(null);
        const messages = ref([]);
        
        // 菜单状态
        const openConversationMenuId = ref(null);
        
        // 消息状态
        const newMessage = ref('');
        const isSending = ref(false);
        const isReceiving = ref(false);
        
        // DOM引用
        const messagesContainer = ref(null);
        const messageInput = ref(null);
        
        // 格式化时间
        const formatTime = (timestamp) => {
            const date = new Date(timestamp);
            return date.toLocaleTimeString('zh-CN', { hour: '2-digit', minute: '2-digit' });
        };
        
        // 切换侧边栏
        const toggleSidebar = () => {
            isSidebarCollapsed.value = !isSidebarCollapsed.value;
        };
        
        // 切换对话菜单
        const toggleConversationMenu = (conversationId) => {
            if (openConversationMenuId.value === conversationId) {
                openConversationMenuId.value = null;
            } else {
                openConversationMenuId.value = conversationId;
            }
        };
        
        // 点击其他地方关闭菜单
        const handleClickOutside = (event) => {
            if (openConversationMenuId.value) {
                // 检查点击的元素是否在菜单内部
                const menuElements = document.querySelectorAll('.conversation-menu');
                let isClickInsideMenu = false;
                
                menuElements.forEach(element => {
                    if (element.contains(event.target)) {
                        isClickInsideMenu = true;
                    }
                });
                
                // 如果点击不在菜单内部，则关闭菜单
                if (!isClickInsideMenu) {
                    openConversationMenuId.value = null;
                }
            }
        };
        
        // 置顶对话
        const pinConversation = (conversationId) => {
            openConversationMenuId.value = null;
            
            // 找到要置顶的对话
            const index = conversations.value.findIndex(c => c.id === conversationId);
            if (index !== -1) {
                // 将对话移到数组开头
                const [conversation] = conversations.value.splice(index, 1);
                conversations.value.unshift(conversation);
                
                // 如果置顶的是当前对话，保持选中状态
                if (currentConversationId.value === conversationId) {
                    // 无需特殊处理，因为只是改变了顺序
                }
            }
        };
        
        // 处理登出
        const handleLogout = () => {
            localStorage.removeItem('token');
            localStorage.removeItem('currentUser');
            window.location.href = '/static/login.html';
        };
        
        // 加载对话列表
        const loadConversations = async () => {
            try {
                const response = await fetch('/api/conversations', {
                    headers: {
                        'Authorization': `Bearer ${token.value}`
                    }
                });
                
                if (response.ok) {
                    const data = await response.json();
                    conversations.value = data.map(conv => ({
                        id: conv.id,
                        title: conv.title || `对话 ${conv.id}`,
                        preview: '暂无消息',
                        created_at: conv.created_at
                    }));
                    
                    // 如果有对话，默认选择第一个
                    if (data.length > 0 && !currentConversationId.value) {
                        currentConversationId.value = data[0].id;
                        await loadMessages(data[0].id);
                    }
                }
            } catch (error) {
                console.error('Load conversations error:', error);
            }
        };
        
        // 创建新对话
        const createNewConversation = async () => {
            try {
                const response = await fetch('/api/conversations/new', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'Authorization': `Bearer ${token.value}`
                    },
                    body: JSON.stringify({
                        title: `对话 ${new Date().toLocaleDateString('zh-CN')} ${new Date().toLocaleTimeString('zh-CN')}`
                    })
                });
                
                if (response.ok) {
                    const newConversation = await response.json();
                    conversations.value.unshift({
                        id: newConversation.id,
                        title: newConversation.title || `对话 ${newConversation.id}`,
                        preview: '暂无消息',
                        created_at: newConversation.created_at
                    });
                    
                    selectConversation(newConversation.id);
                }
            } catch (error) {
                console.error('Create conversation error:', error);
            }
        };
        
        // 选择对话
        const selectConversation = async (conversationId) => {
            if (currentConversationId.value === conversationId) return;
            
            currentConversationId.value = conversationId;
            messages.value = [];
            await loadMessages(conversationId);
        };
        
        // 删除对话
        const deleteConversation = async (conversationId) => {
            if (conversations.value.length <= 1) {
                alert('至少保留一个对话');
                openConversationMenuId.value = null;
                return;
            }
            
            if (!confirm('确定要删除这个对话吗？')) {
                openConversationMenuId.value = null;
                return;
            }
            
            try {
                const response = await fetch(`/api/conversations/${conversationId}`, {
                    method: 'DELETE',
                    headers: {
                        'Authorization': `Bearer ${token.value}`
                    }
                });
                
                if (response.ok) {
                    // 从列表中移除
                    const index = conversations.value.findIndex(c => c.id === conversationId);
                    if (index !== -1) {
                        conversations.value.splice(index, 1);
                    }
                    
                    // 如果删除的是当前对话，切换到第一个对话
                    if (currentConversationId.value === conversationId) {
                        if (conversations.value.length > 0) {
                            selectConversation(conversations.value[0].id);
                        } else {
                            currentConversationId.value = null;
                            messages.value = [];
                        }
                    }
                }
            } catch (error) {
                console.error('Delete conversation error:', error);
            } finally {
                openConversationMenuId.value = null;
            }
        };
        
        // 加载消息
        const loadMessages = async (conversationId) => {
            try {
                const response = await fetch(`/api/messages?conversation_id=${conversationId}`, {
                    headers: {
                        'Authorization': `Bearer ${token.value}`
                    }
                });
                
                if (response.ok) {
                    const data = await response.json();
                    messages.value = data[conversationId] || [];
                    scrollToBottom();
                }
            } catch (error) {
                console.error('Load messages error:', error);
            }
        };
        
        // 发送消息
        const sendMessage = async () => {
            const message = inputMessage.value.trim();
            if (!message || isSending.value) return;
            
            inputMessage.value = '';
            
            // 添加用户消息到界面
            const userMessage = {
                role: 'user',
                content: message,
                timestamp: new Date().toISOString()
            };
            messages.value.push(userMessage);
            
            scrollToBottom();
            
            isSending.value = true;
            isReceiving.value = true;
            
            try {
                // 添加AI消息占位符
                const aiMessageIndex = messages.value.length;
                messages.value.push({
                    role: 'assistant',
                    content: '',
                    timestamp: new Date().toISOString()
                });
                
                // 发起流式请求
                const response = await fetch('/api/chat/stream', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'Authorization': `Bearer ${token.value}`
                    },
                    body: JSON.stringify({
                        conversation_id: currentConversationId.value,
                        message: message,
                        collection_name: 'FinancialResearchOffice',
                        input: {}
                    })
                });
                
                if (!response.ok) {
                    throw new Error(`HTTP error! status: ${response.status}`);
                }
                
                const reader = response.body.getReader();
                const decoder = new TextDecoder('utf-8');
                let assistantResponse = '';
                
                while (true) {
                    const { done, value } = await reader.read();
                    
                    if (done) {
                        break;
                    }
                    
                    const chunk = decoder.decode(value, { stream: true });
                    const lines = chunk.split('\n\n');
                    
                    for (const line of lines) {
                        if (line.startsWith('data: ')) {
                            const data = line.substring(6);
                            
                            if (data === '[DONE]') {
                                // 流结束
                                isReceiving.value = false;
                                break;
                            }
                            
                            try {
                                const parsedData = JSON.parse(data);
                                // 检查业务code
                                if (parsedData.code === 200 && parsedData.data?.content) {
                                    // 更新AI回复内容
                                    assistantResponse += parsedData.data.content;
                                    messages.value[aiMessageIndex].content = assistantResponse;
                                    scrollToBottom();
                                } else if (parsedData.code !== 200 && parsedData.data?.error) {
                                    // 处理错误
                                    messages.value[aiMessageIndex].content = `错误: ${parsedData.data.error}`;
                                    isReceiving.value = false;
                                    break;
                                }
                            } catch (e) {
                                console.error('解析数据失败:', e);
                            }
                        }
                    }
                    
                    if (!isReceiving.value) {
                        break;
                    }
                }
            } catch (error) {
                console.error('Send message error:', error);
                messages.value.push({
                    role: 'assistant',
                    content: `错误: ${error.message}`,
                    timestamp: new Date().toISOString()
                });
                isReceiving.value = false;
            } finally {
                isSending.value = false;
                scrollToBottom();
            }
        };
        
        // 处理键盘事件
        const handleKeyDown = (e) => {
            if (e.key === 'Enter' && !e.shiftKey) {
                e.preventDefault();
                sendMessage();
            }
        };
        
        // 滚动到底部
        const scrollToBottom = () => {
            nextTick(() => {
                if (messagesContainer.value) {
                    messagesContainer.value.scrollTop = messagesContainer.value.scrollHeight;
                }
            });
        };
        
        // 检查认证状态
        const checkAuth = () => {
            const storedToken = localStorage.getItem('token');
            const storedUser = localStorage.getItem('currentUser');
            
            if (!storedToken || !storedUser) {
                window.location.href = '/static/login.html';
                return false;
            }
            
            token.value = storedToken;
            currentUser.value = storedUser;
            return true;
        };
        
        // 页面加载时检查认证状态并加载数据
        onMounted(() => {
            if (checkAuth()) {
                loadConversations();
                document.addEventListener('click', handleClickOutside);
            }
        });
        
        return {
            // 状态
            currentUser,
            isSidebarCollapsed,
            conversations,
            currentConversationId,
            messages,
            newMessage,
            isSending,
            isReceiving,
            openConversationMenuId,
            
            // DOM引用
            messagesContainer,
            messageInput,
            
            // 方法
            formatTime,
            toggleSidebar,
            toggleConversationMenu,
            pinConversation,
            handleLogout,
            createNewConversation,
            selectConversation,
            deleteConversation,
            sendMessage,
            handleKeyDown
        };
    }
}).mount('#app');