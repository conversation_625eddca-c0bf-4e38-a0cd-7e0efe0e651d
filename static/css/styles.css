.chat-container {
    height: calc(100vh - 100px);
}

.messages-container {
    height: calc(100vh - 220px);
}

.fade-enter-active, .fade-leave-active {
    transition: opacity 0.5s;
}

.fade-enter, .fade-leave-to {
    opacity: 0;
}

.slide-fade-enter-active {
    transition: all 0.3s ease;
}

.slide-fade-leave-active {
    transition: all 0.3s cubic-bezier(1.0, 0.5, 0.8, 1.0);
}

.slide-fade-enter-from, .slide-fade-leave-to {
    transform: translateX(20px);
    opacity: 0;
}

/* 添加滚动条样式 */
::-webkit-scrollbar {
    width: 6px;
    height: 6px;
}

::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 10px;
}

::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 10px;
}

::-webkit-scrollbar-thumb:hover {
    background: #a1a1a1;
}

/* 消息层级样式 */
.message-item {
    position: relative;
}

.message-item .message-connection {
    position: absolute;
    left: -20px;
    top: 0;
    width: 16px;
    height: 100%;
    border-left: 2px solid #e5e7eb;
    border-bottom: 2px solid #e5e7eb;
    border-bottom-left-radius: 8px;
}

.message-item:last-child .message-connection {
    height: 50%;
}

/* 置顶对话样式 */
.pinned-conversation {
    background: linear-gradient(135deg, #fef3c7 0%, #fde68a 100%);
    border-left: 4px solid #f59e0b;
}

.pinned-conversation .pin-icon {
    color: #f59e0b;
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0%, 100% {
        opacity: 1;
    }
    50% {
        opacity: 0.7;
    }
}

/* 对话分组标题样式 */
.conversation-group-title {
    font-size: 0.75rem;
    font-weight: 600;
    color: #6b7280;
    text-transform: uppercase;
    letter-spacing: 0.05em;
    padding: 0.5rem 1rem;
    margin: 0.5rem 0;
    border-bottom: 1px solid #e5e7eb;
}

/* 消息气泡增强样式 */
.message-bubble {
    position: relative;
    max-width: 75%;
    word-wrap: break-word;
}

.message-bubble.user {
    margin-left: auto;
    background: linear-gradient(135deg, #3b82f6 0%, #6366f1 100%);
    color: white;
}

.message-bubble.assistant {
    margin-right: auto;
    background: #f3f4f6;
    color: #374151;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .message-bubble {
        max-width: 90%;
    }

    .message-item {
        margin-left: 0 !important;
    }

    .message-connection {
        display: none;
    }
}